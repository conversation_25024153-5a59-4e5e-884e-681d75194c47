# Team Fullstack Quick Reference Guide

## 檔案結構概覽
`docs\team-fullstack.txt` 包含 BMad-Method 框架的完整全端開發團隊配置。

## 代理人位置快速索引

### 主要代理人及其行號範圍：

| 代理人 | 行號範圍 | 搜索關鍵詞 | 專業領域 |
|--------|----------|------------|----------|
| **bmad-orchestrator** | 63-194 | `bmad-orchestrator.md` | 主協調者、工作流程管理 |
| **analyst** | 196-258 | `analyst.md` | 業務分析、市場研究 |
| **pm** | 260-322 | `pm.md` | 產品管理、PRD創建 |
| **ux-expert** | 324-371 | `ux-expert.md` | UI/UX設計、前端規格 |
| **architect** | 373-437 | `architect.md` | 系統架構、技術設計 |
| **po** | 439-496 | `po.md` | 產品負責人、待辦管理 |

## 快速搜索指令

### 搜索特定代理人：
```
view(path="docs\team-fullstack.txt", search_query_regex="agent-name\.md", context_lines_before=5, context_lines_after=20)
```

### 讀取特定代理人完整資訊：
```
view(path="docs\team-fullstack.txt", view_range=[start_line, end_line])
```

## 代理人核心能力速查

### 🎭 bmad-orchestrator
- **用途**: 工作流程協調、多代理人任務、角色切換
- **關鍵命令**: `*help`, `*agent`, `*workflow`, `*status`
- **搜索詞**: `bmad-orchestrator`

### 📊 analyst (Mary)
- **用途**: 市場研究、競爭分析、專案發現、文檔現有專案
- **關鍵命令**: `create-project-brief`, `perform-market-research`, `brainstorm`
- **搜索詞**: `analyst`

### 📋 pm (John)
- **用途**: PRD創建、產品策略、功能優先級、路線圖規劃
- **關鍵命令**: `create-prd`, `create-brownfield-prd`, `shard-prd`
- **搜索詞**: `pm\.md`

### 🎨 ux-expert (Sally)
- **用途**: UI/UX設計、線框圖、原型、前端規格
- **關鍵命令**: `create-front-end-spec`, `generate-ui-prompt`
- **搜索詞**: `ux-expert`

### 🏗️ architect (Winston)
- **用途**: 系統設計、架構文檔、技術選型、API設計
- **關鍵命令**: `create-full-stack-architecture`, `create-backend-architecture`
- **搜索詞**: `architect`

### 📝 po (Sarah)
- **用途**: 待辦事項管理、故事細化、驗收標準、衝刺規劃
- **關鍵命令**: `execute-checklist-po`, `create-story`, `validate-story-draft`
- **搜索詞**: `po\.md`

## 工作流程快速參考

### 可用工作流程：
- `brownfield-fullstack.yaml` - 現有專案全端開發
- `brownfield-service.yaml` - 現有專案服務開發
- `brownfield-ui.yaml` - 現有專案UI開發
- `greenfield-fullstack.yaml` - 新專案全端開發
- `greenfield-service.yaml` - 新專案服務開發
- `greenfield-ui.yaml` - 新專案UI開發

## 使用指南

### 步驟1: 分析任務需求
根據任務類型選擇合適的代理人：
- **規劃階段** → analyst, pm, architect
- **設計階段** → ux-expert, architect
- **開發階段** → po (故事管理)
- **協調階段** → bmad-orchestrator

### 步驟2: 精確搜索
使用搜索關鍵詞快速定位代理人資訊，而非讀取整個檔案。

### 步驟3: 讀取相關部分
使用 view_range 只讀取需要的代理人配置部分。

## 常用搜索模式

### 搜索代理人配置：
```regex
agent:\s*name:\s*代理人名稱
```

### 搜索命令列表：
```regex
commands:
```

### 搜索依賴項：
```regex
dependencies:
```

## 效率提示

1. **優先使用搜索** - 不要讀取整個檔案
2. **記住行號範圍** - 常用代理人的位置
3. **使用關鍵詞搜索** - 比行號更靈活
4. **批量獲取資訊** - 一次搜索獲取所需的所有資訊

## 範例使用場景

### 場景1: 需要創建PRD
1. 搜索 `pm\.md` 找到產品經理配置
2. 讀取 pm 代理人的命令和能力
3. 使用 `create-prd` 命令

### 場景2: 需要系統架構設計
1. 搜索 `architect` 找到架構師配置
2. 讀取相關命令和模板
3. 選擇適當的架構創建命令

### 場景3: 需要多代理人協作
1. 搜索 `bmad-orchestrator` 找到協調者
2. 使用工作流程指導功能
3. 按需載入其他代理人
