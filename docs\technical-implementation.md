# VeilView 技術實現指南

## 🏗️ 系統架構設計

### 整體架構圖
```
┌─────────────────────────────────────────────────────────┐
│                    VeilView Mobile App                  │
├─────────────────────────────────────────────────────────┤
│  Presentation Layer (React Native + TypeScript)        │
│  ├── Disguise Components (Weather, Fitness, etc.)      │
│  ├── Real Chart Components (Traditional Views)         │
│  ├── Navigation & Routing                              │
│  └── Authentication & Security UI                      │
├─────────────────────────────────────────────────────────┤
│  Business Logic Layer                                  │
│  ├── Data Transformation Engine                        │
│  ├── Disguise Mode Controller                          │
│  ├── Alert & Notification Manager                      │
│  ├── Portfolio Management Service                      │
│  └── Auto-Disguise Intelligence                        │
├─────────────────────────────────────────────────────────┤
│  Data Access Layer                                     │
│  ├── Stock Data Service (Multi-API)                    │
│  ├── Real-time WebSocket Manager                       │
│  ├── Local Cache Manager (SQLite)                      │
│  ├── User Preferences Store                            │
│  └── Security & Encryption Service                     │
├─────────────────────────────────────────────────────────┤
│  External Services                                     │
│  ├── Stock APIs (Alpha Vantage, IEX, Polygon)         │
│  ├── Push Notification Service (FCM)                   │
│  ├── Analytics Service (Privacy-focused)               │
│  └── Crash Reporting (Sentry)                          │
└─────────────────────────────────────────────────────────┘
```

## 📦 技術棧詳細配置

### 核心框架
```json
{
  "expo": "~49.0.0",
  "react-native": "0.72.6",
  "typescript": "^5.1.3",
  "react": "18.2.0"
}
```

### 狀態管理
```typescript
// Zustand 配置
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface AppState {
  // 偽裝模式狀態
  currentDisguiseMode: DisguiseMode
  isDisguiseActive: boolean
  autoDisguiseSettings: AutoDisguiseSettings
  
  // 股票數據狀態
  watchlist: Stock[]
  portfolios: Portfolio[]
  realTimeData: Map<string, StockData>
  
  // 用戶設置
  userPreferences: UserPreferences
  securitySettings: SecuritySettings
}

const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 狀態和動作定義
    }),
    {
      name: 'veilview-storage',
      // 只持久化必要數據
      partialize: (state) => ({
        userPreferences: state.userPreferences,
        watchlist: state.watchlist,
        portfolios: state.portfolios
      })
    }
  )
)
```

### 圖表和視覺化
```typescript
// Victory Native 配置
import { VictoryChart, VictoryLine, VictoryArea } from 'victory-native'

// React Native Skia 自定義繪圖
import { Canvas, Path, Skia } from '@shopify/react-native-skia'

// 偽裝模式圖表組件
const WeatherChart: React.FC<WeatherChartProps> = ({ stockData }) => {
  const transformedData = transformStockToWeather(stockData)
  
  return (
    <Canvas style={{ flex: 1 }}>
      {/* 自定義天氣圖表繪製 */}
    </Canvas>
  )
}
```

### 網絡和數據管理
```typescript
// React Query 配置
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30000, // 30秒
      cacheTime: 300000, // 5分鐘
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)
    }
  }
})

// 股票數據服務
class StockDataService {
  private apiClients: ApiClient[]
  private wsManager: WebSocketManager
  
  async getStockData(symbol: string): Promise<StockData> {
    // 多API源策略
    for (const client of this.apiClients) {
      try {
        return await client.getStockData(symbol)
      } catch (error) {
        console.warn(`API ${client.name} failed, trying next...`)
      }
    }
    throw new Error('All API sources failed')
  }
  
  subscribeToRealTime(symbols: string[], callback: (data: StockData) => void) {
    this.wsManager.subscribe(symbols, callback)
  }
}
```

## 🎭 偽裝模式實現

### 數據轉換引擎
```typescript
interface DataTransformer {
  transform(stockData: StockData, mode: DisguiseMode): DisguiseData
}

class WeatherTransformer implements DataTransformer {
  transform(stockData: StockData, mode: 'weather'): WeatherData {
    const { price, change, changePercent, volume, high, low } = stockData
    
    // 溫度映射 (股價 -> 溫度)
    const temperature = this.mapPriceToTemperature(price, low, high)
    
    // 濕度映射 (波動率 -> 濕度)
    const humidity = this.mapVolatilityToHumidity(changePercent)
    
    // 風速映射 (成交量 -> 風速)
    const windSpeed = this.mapVolumeToWindSpeed(volume)
    
    // 天氣圖標 (漲跌 -> 天氣)
    const weatherIcon = this.mapChangeToWeatherIcon(changePercent)
    
    return {
      temperature,
      humidity,
      windSpeed,
      weatherIcon,
      forecast: this.generateForecast(stockData.history)
    }
  }
  
  private mapPriceToTemperature(price: number, low: number, high: number): number {
    // 將股價映射到 -10°C 到 50°C 範圍
    const range = high - low
    const position = (price - low) / range
    return Math.round(-10 + position * 60)
  }
  
  private mapChangeToWeatherIcon(changePercent: number): WeatherIcon {
    if (changePercent > 5) return '⛈️' // 大漲
    if (changePercent > 2) return '☀️' // 上漲
    if (changePercent > -2) return '⛅' // 平穩
    if (changePercent > -5) return '🌧️' // 下跌
    return '⛈️' // 大跌
  }
}
```

### 偽裝模式控制器
```typescript
class DisguiseModeController {
  private currentMode: DisguiseMode = 'real'
  private autoDisguiseEnabled = false
  private sensors: SensorManager
  
  constructor() {
    this.sensors = new SensorManager()
    this.setupAutoDisguise()
  }
  
  async switchMode(mode: DisguiseMode, animated = true): Promise<void> {
    if (animated) {
      await this.animateTransition(this.currentMode, mode)
    }
    this.currentMode = mode
    this.notifyModeChange(mode)
  }
  
  enableAutoDisguise(settings: AutoDisguiseSettings): void {
    this.autoDisguiseEnabled = true
    this.sensors.enableProximitySensor()
    this.sensors.enableLocationTracking()
    this.setupTimeBasedDisguise(settings.timeRules)
  }
  
  private setupAutoDisguise(): void {
    // 接近感應器
    this.sensors.onProximityChange((isNear) => {
      if (isNear && this.autoDisguiseEnabled) {
        this.emergencyDisguise()
      }
    })
    
    // 位置感知
    this.sensors.onLocationChange((location) => {
      if (this.isWorkLocation(location)) {
        this.switchMode('weather') // 默認工作場所偽裝
      }
    })
  }
  
  private async emergencyDisguise(): Promise<void> {
    // 緊急偽裝：最快速度切換
    await this.switchMode('weather', false)
  }
}
```

## 📊 實時數據管理

### WebSocket 管理器
```typescript
class WebSocketManager {
  private connections: Map<string, WebSocket> = new Map()
  private reconnectAttempts: Map<string, number> = new Map()
  private maxReconnectAttempts = 5
  
  subscribe(symbols: string[], callback: (data: StockData) => void): void {
    symbols.forEach(symbol => {
      this.createConnection(symbol, callback)
    })
  }
  
  private createConnection(symbol: string, callback: (data: StockData) => void): void {
    const ws = new WebSocket(`wss://api.example.com/stocks/${symbol}`)
    
    ws.onopen = () => {
      console.log(`WebSocket connected for ${symbol}`)
      this.reconnectAttempts.set(symbol, 0)
    }
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        callback(this.parseStockData(data))
      } catch (error) {
        console.error('Failed to parse WebSocket data:', error)
      }
    }
    
    ws.onclose = () => {
      this.handleReconnect(symbol, callback)
    }
    
    ws.onerror = (error) => {
      console.error(`WebSocket error for ${symbol}:`, error)
    }
    
    this.connections.set(symbol, ws)
  }
  
  private handleReconnect(symbol: string, callback: (data: StockData) => void): void {
    const attempts = this.reconnectAttempts.get(symbol) || 0
    
    if (attempts < this.maxReconnectAttempts) {
      const delay = Math.min(1000 * Math.pow(2, attempts), 30000)
      
      setTimeout(() => {
        this.reconnectAttempts.set(symbol, attempts + 1)
        this.createConnection(symbol, callback)
      }, delay)
    }
  }
}
```

### 數據緩存策略
```typescript
class DataCacheManager {
  private db: SQLiteDatabase
  private memoryCache: Map<string, CacheEntry> = new Map()
  
  async getStockData(symbol: string): Promise<StockData | null> {
    // 1. 檢查內存緩存
    const memoryEntry = this.memoryCache.get(symbol)
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      return memoryEntry.data
    }
    
    // 2. 檢查本地數據庫
    const dbData = await this.db.get(`
      SELECT * FROM stock_data 
      WHERE symbol = ? AND timestamp > ?
    `, [symbol, Date.now() - 300000]) // 5分鐘內的數據
    
    if (dbData) {
      this.memoryCache.set(symbol, {
        data: dbData,
        timestamp: Date.now()
      })
      return dbData
    }
    
    return null
  }
  
  async cacheStockData(symbol: string, data: StockData): Promise<void> {
    // 更新內存緩存
    this.memoryCache.set(symbol, {
      data,
      timestamp: Date.now()
    })
    
    // 更新數據庫
    await this.db.run(`
      INSERT OR REPLACE INTO stock_data 
      (symbol, price, change, volume, timestamp) 
      VALUES (?, ?, ?, ?, ?)
    `, [symbol, data.price, data.change, data.volume, Date.now()])
  }
}
```

## 🔐 安全實現

### 數據加密
```typescript
import CryptoJS from 'crypto-js'
import * as SecureStore from 'expo-secure-store'

class SecurityService {
  private encryptionKey: string
  
  constructor() {
    this.initializeEncryption()
  }
  
  private async initializeEncryption(): Promise<void> {
    let key = await SecureStore.getItemAsync('encryption_key')
    if (!key) {
      key = CryptoJS.lib.WordArray.random(256/8).toString()
      await SecureStore.setItemAsync('encryption_key', key)
    }
    this.encryptionKey = key
  }
  
  encrypt(data: any): string {
    const jsonString = JSON.stringify(data)
    return CryptoJS.AES.encrypt(jsonString, this.encryptionKey).toString()
  }
  
  decrypt(encryptedData: string): any {
    const bytes = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey)
    const decryptedString = bytes.toString(CryptoJS.enc.Utf8)
    return JSON.parse(decryptedString)
  }
  
  async storeSecureData(key: string, data: any): Promise<void> {
    const encryptedData = this.encrypt(data)
    await SecureStore.setItemAsync(key, encryptedData)
  }
  
  async getSecureData(key: string): Promise<any> {
    const encryptedData = await SecureStore.getItemAsync(key)
    if (!encryptedData) return null
    return this.decrypt(encryptedData)
  }
}
```

### API 密鑰保護
```typescript
// 使用代理服務器保護API密鑰
class SecureApiClient {
  private baseUrl = 'https://your-proxy-server.com/api'
  
  async makeRequest(endpoint: string, params: any): Promise<any> {
    const response = await fetch(`${this.baseUrl}/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${await this.getClientToken()}`
      },
      body: JSON.stringify(params)
    })
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`)
    }
    
    return response.json()
  }
  
  private async getClientToken(): Promise<string> {
    // 客戶端認證邏輯
    // 避免在客戶端暴露真實API密鑰
  }
}
```

## ⚡ 性能優化

### 動畫優化
```typescript
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS
} from 'react-native-reanimated'

const DisguiseModeTransition: React.FC = ({ children, mode }) => {
  const opacity = useSharedValue(1)
  const scale = useSharedValue(1)
  
  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }]
  }))
  
  const switchMode = useCallback((newMode: DisguiseMode) => {
    // 淡出動畫
    opacity.value = withTiming(0, { duration: 150 }, () => {
      // 切換模式
      runOnJS(setCurrentMode)(newMode)
      
      // 淡入動畫
      opacity.value = withTiming(1, { duration: 150 })
    })
  }, [])
  
  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      {children}
    </Animated.View>
  )
}
```

### 內存管理
```typescript
class MemoryManager {
  private static instance: MemoryManager
  private dataCache: Map<string, any> = new Map()
  private maxCacheSize = 100
  
  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager()
    }
    return MemoryManager.instance
  }
  
  set(key: string, value: any): void {
    if (this.dataCache.size >= this.maxCacheSize) {
      // LRU 清理策略
      const firstKey = this.dataCache.keys().next().value
      this.dataCache.delete(firstKey)
    }
    this.dataCache.set(key, value)
  }
  
  get(key: string): any {
    const value = this.dataCache.get(key)
    if (value) {
      // 移到最後（LRU）
      this.dataCache.delete(key)
      this.dataCache.set(key, value)
    }
    return value
  }
  
  clear(): void {
    this.dataCache.clear()
  }
}
```

## 📱 平台特定實現

### Android 優化
```typescript
// Android 後台任務優化
import BackgroundJob from '@react-native-async-storage/async-storage'

class AndroidOptimizations {
  static setupBackgroundSync(): void {
    // 使用 Android WorkManager 進行後台同步
    BackgroundJob.start({
      taskName: 'stockDataSync',
      taskTitle: 'Syncing stock data',
      taskDesc: 'Updating your portfolio data',
      taskIcon: {
        name: 'ic_launcher',
        type: 'mipmap',
      }
    })
  }
  
  static optimizeBatteryUsage(): void {
    // 電池優化設置
    // 請求用戶將應用加入電池優化白名單
  }
}
```

### iOS 準備
```typescript
// iOS 特定功能準備
class IOSOptimizations {
  static setupBackgroundRefresh(): void {
    // iOS 背景應用程式重新整理
  }
  
  static configureNotifications(): void {
    // iOS 通知配置
  }
}
```

這個技術實現指南提供了 VeilView 應用的核心技術架構和實現細節。每個模組都經過精心設計，確保應用的性能、安全性和用戶體驗。
